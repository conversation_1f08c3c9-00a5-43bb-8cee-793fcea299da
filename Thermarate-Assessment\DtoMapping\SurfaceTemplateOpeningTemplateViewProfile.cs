﻿using AutoMapper;
using RediSoftware.Dtos;
using RediSoftware.Models;
using AutoMapper.QueryableExtensions;

namespace RediSoftware.DtoMapping
{
    public class SurfaceTemplateOpeningTemplateViewProfile : Profile
    {
        public SurfaceTemplateOpeningTemplateViewProfile()
        {
            // Map from Table to DTO
            CreateMap<RSS_SurfaceTemplateOpeningTemplateView, SurfaceOpeningViewDto>()
                .ForMember(dest => dest.InsulationDescription, opt => opt.MapFrom(src => src.InsulationDescription))
            ;
        }
    }
}