(function () {
    // The ConstructionListCtrl supports a list page.
    'use strict';
    var controllerId = 'ConstructionListCtrl';
    angular.module('app')
    .controller(controllerId, ['$q', '$rootScope', '$stateParams', '$scope', '$mdDialog', 'constructionservice', 'manufacturerservice', 'daterangehelper', 'common', '$state', '$timeout', constructionListController]);
    function constructionListController($q, $rootScope, $stateParams, $scope, $mdDialog, constructionservice, manufacturerservice, daterangehelper, common, $state, $timeout) {
        // The model for this form
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        vm.title = $stateParams.type + ' Database';
        vm.constructionList = [];
        vm.listFilter = "";
        vm.actionButtons = [];

        // Multi-filter properties
        vm.searchString = null;
        vm.searchStringOld = null;
        vm.searchFields = ['description', 'constructionCategoryTitle', 'constructionSubCategoryTitle', 'openingStyleTitle', 'manufacturerDescription', 'insulationDescription'];
        vm.filters = [
            { section: 1, name: 'category', field: 'constructionCategoryTitle' },
            { section: 1, name: 'constructionType', field: $stateParams.type.toLowerCase() === 'construction' ? 'constructionSubCategoryTitle' : 'openingStyleTitle' }
        ];

        // Add insulation filter only for construction type
        if ($stateParams.type.toLowerCase() === 'construction') {
            vm.filters.push({ section: 1, name: 'insulation', field: 'insulationDescription' });
        }
        vm.filteredFilters = vm.filters.filter(f => f.name != null && f.name != "[blank]" && f.name != "[moreLessButton]");
        vm.filtersExpanded = false;
        vm.filterOptions = {};
        vm.appliedFilters = {};
        vm.appliedFiltersOld = {};
        vm.filterCountData = {};
        vm.cachedFilterCounts = {}; // Cache for filter counts
        vm.cachedGroupCounts = {}; // Cache for group counts
        vm.cachedGroupOptions = {}; // Cache for group options
        vm.filtersApplied = false;
        vm.totalFilteredConstructions = 0;
        vm.totalConstructions = 0;
        vm.showingFromCnt = 0;
        vm.showingToCnt = 0;
        vm.initialiseComplete = false;

        // Get all filters with group headings removed
        vm.filteredAppliedFilters = function () {
            let newFilters = angular.copy(vm.appliedFilters);
            Object.keys(newFilters).forEach(fieldName => {
                if (vm.filters.find(f => f.field == fieldName)?.groups != null) {
                    newFilters[fieldName] = newFilters[fieldName].filter(o => !o.startsWith("optionGroup"));
                }
            });
            return newFilters;
        }

        // Multi-filter helper functions
        vm.keyToName = function (key) {
            switch (key) {
                case 'category': return 'Category';
                case 'constructionType': return $stateParams.type.toLowerCase() === 'construction' ? 'Construction Type' : 'Opening Style';
                case 'insulation': return 'Insulation';
                default: return key;
            }
        };

        vm.anyOptionsSelectedOnField = common.anyOptionsSelectedOnField;
        vm.getFilterSelectedText = common.getMultiFilterSelectedText;
        vm.optionName = common.getOptionName;
        vm.anyFiltersApplied = common.anyFiltersApplied;

        vm.clearFilter = function (filter) {
            vm.appliedFilters[filter.field] = filter.isDate ? daterangehelper.getDefaultRange('All Time') : [];
            vm.filtersApplied = vm.anyFiltersApplied(vm.searchString, vm.filteredFilters, vm.appliedFilters);

            // Clear caches related to this filter
            if (filter && filter.field) {
                const fieldName = filter.field;

                // Clear filter count cache for this field
                Object.keys(vm.cachedFilterCounts).forEach(key => {
                    if (key.startsWith(`${fieldName}_`)) {
                        delete vm.cachedFilterCounts[key];
                    }
                });

                // Clear group options cache for this field
                Object.keys(vm.cachedGroupOptions).forEach(key => {
                    if (key.startsWith(`${fieldName}_`)) {
                        delete vm.cachedGroupOptions[key];
                    }
                });

                // Clear group count cache for this field
                Object.keys(vm.cachedGroupCounts).forEach(key => {
                    if (key.startsWith(`${fieldName}_`)) {
                        delete vm.cachedGroupCounts[key];
                    }
                });
            }

            vm.refreshListMultiFiltered();
        };

        vm.clearFilters = function () {
            vm.initialiseComplete = false;
            vm.appliedFilters = {};
            vm.appliedFiltersOld = {};
            vm.filterCountData = {};
            vm.clearFilterCaches();
            vm.searchString = null;
            vm.searchStringOld = null;
            // Initialize filters with empty arrays (following Jobs pattern)
            vm.filters.forEach(filter => {
                vm.appliedFilters[filter.field] = filter.isDate ? daterangehelper.getDefaultRange('All Time') : [];
            });
            vm.filtersApplied = false;
            vm.initialRefreshList();
        };

        // Create a lookup map for faster filter count lookups
        vm.updateFilterCountLookup = function() {
            vm.filterCountLookup = {};

            if (!vm.filterCountData) return;

            Object.keys(vm.filterCountData).forEach(fieldName => {
                vm.filterCountLookup[fieldName] = {};

                if (vm.filterCountData[fieldName]) {
                    Object.keys(vm.filterCountData[fieldName]).forEach(key => {
                        vm.filterCountLookup[fieldName][key.toLowerCase()] = vm.filterCountData[fieldName][key];
                    });
                }
            });
        }

        // Optimized function to get filter count - uses caching and lookup map
        vm.getFilterCountItem = function (filter, item) {
            if (!item) return null;

            let fieldName = filter.field;
            let itemValue = item.value?.toString().toLowerCase();

            if (!itemValue) return null;

            // Use cache if available
            let cacheKey = `${fieldName}_${itemValue}`;
            if (vm.cachedFilterCounts[cacheKey] !== undefined) {
                return vm.cachedFilterCounts[cacheKey];
            }

            // Use lookup map for faster access
            if (vm.filterCountLookup && vm.filterCountLookup[fieldName]) {
                const count = vm.filterCountLookup[fieldName][itemValue];
                vm.cachedFilterCounts[cacheKey] = count || 0;
                return count || 0;
            }

            // Fallback to original method if lookup not available
            if (!vm.filterCountData[fieldName]) {
                vm.cachedFilterCounts[cacheKey] = 0;
                return 0;
            }

            let valueKey = Object.keys(vm.filterCountData[fieldName]).find(option =>
                option.toLowerCase() === itemValue);

            const count = valueKey ? vm.filterCountData[fieldName][valueKey] : 0;
            vm.cachedFilterCounts[cacheKey] = count;
            return count;
        }

        // Optimized function to get group options - uses caching
        vm.getGroupOptions = function (filter, group) {
            let fieldName = filter.field;
            let groupKey = `${fieldName}_${group.value}`;

            // Return cached result if available
            if (vm.cachedGroupOptions[groupKey]) {
                return vm.cachedGroupOptions[groupKey];
            }

            let filteredFilterOptions = vm.filterOptions[fieldName]?.filter(f => group.startRange > group.endRange
                                                                                 ? +f.value > group.startRange || +f.value <= group.endRange
                                                                                 : +f.value > group.startRange && +f.value <= group.endRange);

            // Create a lookup map for faster filtering
            if (!vm.filterCountLookup) {
                vm.updateFilterCountLookup();
            }

            filteredFilterOptions = filteredFilterOptions?.filter(o => {
                const countItem = vm.getFilterCountItem(filter, o);
                return countItem > 0;
            });

            // Cache the result
            vm.cachedGroupOptions[groupKey] = filteredFilterOptions;
            return filteredFilterOptions;
        }

        // Optimized function to get group count - uses caching
        vm.getGroupCount = function (filter, group) {
            let cacheKey = `${filter.field}_${group.value}`;

            // Return cached result if available
            if (vm.cachedGroupCounts[cacheKey] !== undefined) {
                return vm.cachedGroupCounts[cacheKey];
            }

            let optionsForThisGroup = vm.getGroupOptions(filter, group);
            if (!optionsForThisGroup || optionsForThisGroup.length === 0) {
                vm.cachedGroupCounts[cacheKey] = 0;
                return 0;
            }

            // Calculate the total once and cache it
            let thisGroupTotal = 0;
            for (let i = 0; i < optionsForThisGroup.length; i++) {
                const count = vm.getFilterCountItem(filter, optionsForThisGroup[i]);
                thisGroupTotal += count || 0;
            }

            vm.cachedGroupCounts[cacheKey] = thisGroupTotal;
            return thisGroupTotal;
        }

        // Clear caches when filter data changes
        vm.clearFilterCaches = function() {
            vm.cachedFilterCounts = {};
            vm.cachedGroupCounts = {};
            vm.cachedGroupOptions = {};
            vm.filterCountLookup = null;
        }

        // Add missing group-related functions from Jobs controller
        vm.selectOptionForGroup = function (filter, selected, group) {
            let fieldName = filter.field;
            let allOptionsForThisGroup = [...vm.getGroupOptions(filter, group).map(o => o.value), group.value];
            // function selectAllLogic(allOptions, listBefore, listChildIdKey, selected, selectAllCode)
            let newSelectedForThisGroup = common.selectAllLogic(
                [...allOptionsForThisGroup, group.value],
                vm.appliedFilters[fieldName].filter(o => allOptionsForThisGroup.includes(o)), // Only looking at this group's selected options
                null,
                selected.value,
                group.value
            )
            // Now that we have new selected options for this group, add this group's selections to full list of selections by removing this group's options from full selected list then adding this group's new selected
            vm.appliedFilters[fieldName] = vm.appliedFilters[fieldName].filter(o => !allOptionsForThisGroup.includes(o)).concat(newSelectedForThisGroup);
        }

        vm.currentQuery = {};

        vm.templateType = $scope.type ?? $stateParams.type;

        const FILTER_NAME = "constructionListCtrl-customFilter";

        vm.queryModel = {
            canSave: false,
            fields: [
                {
                    name: 'constructionCategoryCode',
                    description: 'Category',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'description',
                    description: 'Description',
                    dataType: 'string',
                    operators: []
                },
            ],
        };

        vm.constructionCategories = [];
        constructionservice.getConstructionCategoryList()
            .then((data) => {
                vm.constructionCategories = data;

                // Restrict selection by type...
                if (vm.templateType.toLowerCase() == "construction")
                    vm.constructionCategories = constructionservice.constructionCategories();
                else if (vm.templateType.toLowerCase() == "opening")
                    vm.constructionCategories = constructionservice.openingCategories();

                console.log('vm.templateType:', vm.templateType.toLowerCase());
                console.log('vm.constructionCategories:', vm.constructionCategories);
            });

        // Construction Only
        // vm.constructionSubCategories = [];

        // Openings Only
        vm.openingStyles = [];

        // if (vm.templateType.toLowerCase() == "construction") {
        //     constructionservice.getConstructionSubCategoryList()
        //     .then((data) => {
        //         vm.constructionSubCategories = data;
        //         // No need to restrict as one or the other.
        //         console.log('vm.constructionSubCategories:', vm.constructionSubCategories);
        //     });
        // }
        // else
        if (vm.templateType.toLowerCase() == "opening") {
            constructionservice.getOpeningStyleList()
            .then((data) => {
                vm.openingStyles = data;
                // No need to restrict as one or the other.
                console.log('vm.openingStyles:', vm.openingStyles);
            });
        }

        vm.manufacturers = [];
        manufacturerservice.getAllManufacturersList().then((data) => { vm.manufacturers = data; });

        // Initialize multi-filters - matches Jobs pattern exactly
        vm.initialRefreshList = function () {
            vm.initialiseComplete = false;

            // Use Promise.all to make concurrent API calls
            Promise.all([
                constructionservice.getListV2($stateParams.type.toLowerCase(), vm.listFilter, vm.rptDateRange.startDate.toISOString(), vm.rptDateRange.endDate.toISOString(), 100, 1, null, null),
                constructionservice.getMultiFilterOptions(vm.filteredFilters, $stateParams.type.toLowerCase())
            ]).then(results => {
                // Process constructions data (first promise result)
                const constructionsResult = results[0];
                vm.constructionList = constructionsResult.data;
                vm.totalConstructions = constructionsResult.total;
                vm.totalFilteredConstructions = vm.totalConstructions;

                // Sort favorited items to the top
                if (vm.constructionList && vm.constructionList.length > 0) {
                    vm.constructionList.sort((a, b) => {
                        if (a.isFavourite && !b.isFavourite) return -1;
                        if (!a.isFavourite && b.isFavourite) return 1;
                        return 0;
                    });
                }

                // Process filter options (second promise result)
                const filterOptions = results[1];
                vm.filterOptions = filterOptions;
                common.orderFilterOptions(vm.filterOptions);
                common.adjustFiltersForSelection(vm.filteredFilters, vm.appliedFilters, vm.appliedFiltersOld);
                vm.appliedFiltersOld = angular.copy(vm.appliedFilters);

                // Create search filter
                let searchFilter = [];
                for (let i = 0; i < vm.searchFields.length; i++) {
                    if (i == vm.searchFields.length-1) {
                        searchFilter.push({ field: vm.searchFields[i], operator: "contains", value: vm.searchString })
                    } else {
                        searchFilter.push({ field: vm.searchFields[i], operator: "contains", value: vm.searchString, logic: "or" })
                    }
                }

                // Make additional API call for filter count data after both initial calls are complete
                constructionservice.getFilterCountData(vm.filteredFilters, vm.filterOptions, vm.filteredAppliedFilters(), searchFilter, $stateParams.type.toLowerCase()).then(data => {
                    // Process filter count data
                    vm.filterCountData = data;
                    vm.clearFilterCaches();
                    vm.updateFilterCountLookup();

                    // Update UI
                    vm.showingFromCnt = vm.totalConstructions > 0 ? 1 : 0;
                    vm.showingToCnt = vm.totalConstructions < 100 ? vm.totalConstructions : 100;
                    vm.isBusy = false;
                    $timeout(() => vm.initialiseComplete = true, 1000);
                }).catch(error => {
                    console.error('Error loading filter count data:', error);
                    vm.initialiseComplete = true;
                    vm.isBusy = false;
                });
            }).catch(error => {
                vm.isBusy = false;
                console.error('Error initializing construction list:', error);
            });
        };

        var persistRangeName = "constructionList-DtRange";
        vm.rptDateRange = daterangehelper.getDefaultRange('All Time', persistRangeName);
        vm.ranges = daterangehelper.getRanges('Today', 'Yesterday', 'This Week', 'Last Week', 'This Month', 'Last Month',
                                                'This Quarter', 'Last Quarter', 'Current Year', 'Current Financial Year', 'Last Financial Year',
                                                'Last Year', '12 Months', 'All Time');

        //Repopulate the List after Refresh Page
        vm.refreshList = function (filter) {
            vm.callServer(null, null, filter);
            localStorage.setItem(persistRangeName, JSON.stringify(vm.rptDateRange));
        };

        // Multi-filtered refresh function - matches Jobs pattern exactly
        vm.refreshListMultiFiltered = function () {
            if (vm.initialiseComplete && (!angular.equals(vm.appliedFilters, vm.appliedFiltersOld) || vm.searchString != vm.searchStringOld)) {
                vm.isBusy = true;
                let resultsPageSize = 100;

                // Adjust filters for selection (following Jobs pattern)
                common.adjustFiltersForSelection(vm.filteredFilters, vm.appliedFilters, vm.appliedFiltersOld);
                vm.appliedFiltersOld = angular.copy(vm.appliedFilters);

                // Create search filter
                let searchFilter = [];
                for (let i = 0; i < vm.searchFields.length; i++) {
                    if (i == vm.searchFields.length-1) {
                        searchFilter.push({ field: vm.searchFields[i], operator: "contains", value: vm.searchString })
                    } else {
                        searchFilter.push({ field: vm.searchFields[i], operator: "contains", value: vm.searchString, logic: "or" })
                    }
                }

                // Get filtered constructions
                constructionservice.getListMultiFiltered(resultsPageSize, 1, null, vm.filteredFilters, vm.filterOptions, vm.filteredAppliedFilters(), searchFilter, $stateParams.type.toLowerCase()).then(function (result) {
                    if (result == undefined || result == null)
                        return; // Its been cancelled so get out of here.

                    if (result && result.data) {
                        vm.constructionList = result.data;
                        vm.totalFilteredConstructions = result.total;
                        vm.showingFromCnt = vm.constructionList.length > 0 ? 1 : 0;
                        vm.showingToCnt = vm.constructionList.length;

                        // Sort favorited items to the top
                        if (vm.constructionList && vm.constructionList.length > 0) {
                            vm.constructionList.sort((a, b) => {
                                if (a.isFavourite && !b.isFavourite) return -1;
                                if (!a.isFavourite && b.isFavourite) return 1;
                                return 0;
                            });
                        }
                    }
                    vm.filtersApplied = vm.anyFiltersApplied(vm.searchString, vm.filteredFilters, vm.appliedFilters);
                    vm.searchStringOld = vm.searchString;

                    // Get filter count data and update caches
                    constructionservice.getFilterCountData(vm.filteredFilters, vm.filterOptions, vm.filteredAppliedFilters(), searchFilter, $stateParams.type.toLowerCase()).then(data => {
                        vm.filterCountData = data;
                        vm.clearFilterCaches();
                        vm.updateFilterCountLookup();
                        vm.isBusy = false;
                    });
                }).catch(function (error) {
                    vm.isBusy = false;
                });
            }
        };

        vm.createConstruction = function () {
            var modalScope = $rootScope.$new();
            modalScope.viewMode = "New";
            modalScope.newRecord = true;
            modalScope.type = $stateParams.type;
            var modalOptions = {
                templateUrl: 'app/ui/data/construction/construction-update.html',
                scope: modalScope,
                resolve: {
                    viewMode: function () {
                        return 'New';
                    }
                }
            };
            modalScope.modalInstance = $mdDialog.show(modalOptions);
            modalScope.modalInstance.then(function (data) {
                // Returned from modal, so refresh list.
                vm.refreshList(null);
            }, function () {
                vm.refreshList(null);
                // Cancelled.
            })['finally'](function () {
                modalScope.modalInstance = undefined  // <--- This fixes
            });
        }

        var saveTableState = null;
        vm.callServer = function callServer(tableState, e, filter) {

            if (tableState != null) {
                saveTableState = tableState;
            }
            if (saveTableState == null || vm.currentQuery == null || vm.currentQuery.queryName == null) {
                return;
            }

            var pagination = saveTableState.pagination;

            var start = pagination.start || 0;     // This is NOT the page number, but the index of item in the list that you want to use to display the table.
            var pageSize = pagination.number || 100;  // Number of entries showed per page.
            var pageIndex = (start / pageSize) + 1;

            vm.isBusy = true;
            var sort = {};
            if (saveTableState.sort != null) {
                sort.field = saveTableState.sort.predicate;
                sort.dir = saveTableState.sort.reverse ? "desc" : "asc";
            }

            if (filter == null) {
                if (saveTableState.search != null && saveTableState.search.predicateObject != null && saveTableState.search.predicateObject.$ != null) {
                    var val = saveTableState.search.predicateObject.$;
                    // Adjust here for the columns quick search will search.
                    filter = [
                        { field: "description", operator: "startswith", value: val, logic: "or" },
                        { field: "constructionCategoryTitle", operator: "contains", value: val }
                    ];
                }
                if (vm.currentQuery != null && vm.currentQuery.filter != null && vm.currentQuery.filter.length > 0) {
                    filter = vm.currentQuery.filter;
                }
            }

            daterangehelper.correctRangeDates(vm.rptDateRange);
            constructionservice.getListCancel();
            constructionservice.getListV2($stateParams.type.toLowerCase(), vm.listFilter, vm.rptDateRange.startDate.toISOString(), vm.rptDateRange.endDate.toISOString(), pageSize, pageIndex, sort, filter)
                .then(function (result) {
                    if (result == undefined || result == null) {
                        // Its been cancelled so get out of here.
                        return;
                    }
                    vm.currentFilter = constructionservice.currentFilter();
                    vm.constructionList = result.data;

                    // Sort favorited items to the top
                    if (vm.constructionList && vm.constructionList.length > 0) {
                        vm.constructionList.sort((a, b) => {
                            if (a.isFavourite && !b.isFavourite) return -1;
                            if (!a.isFavourite && b.isFavourite) return 1;
                            return 0;
                        });
                    }

                    vm.totalRecords = result.total;
                    saveTableState.pagination.numberOfPages = Math.ceil(result.total / pageSize); //set the number of pages so the pagination can update
                    vm.showingFromCnt = vm.constructionList.length > 0 ? start + 1 : 0;
                    vm.showingToCnt = start + result.data.length;
                    vm.isBusy = false;
                },
                function (error) {
                    vm.isBusy = false;
                });
        };

        vm.delete = async function (row, showSuccessMessage = true) {
            await constructionservice
                .deleteConstruction(row.constructionId, row.type, showSuccessMessage);

            row.deleted = true;
            vm.constructionList = vm.constructionList.filter(x => x.constructionId != row.constructionId);
        }

        vm.clone = function (row, showSuccessMessage = true) {

            constructionservice
                .copyConstruction(row.constructionId, row.type, showSuccessMessage)
                .then((id) => {

                    constructionservice
                        .getConstruction(id, row.type)
                        .then(template => {

                            // Transform the returned construction
                            // so it matches our view data.
                            let transform = {
                                constructionId: template.constructionId,
                                description: template.description,
                                constructionCategoryTitle: template.category.title,
                                type: template.category.type == 'surface' ? 'surface' : 'opening'
                            };

                        // Add returned template to list.
                            vm.constructionList.push(transform);

                            // Ideally sort based on what we're actually sorting by, but since
                            // we basically only have the Template Name to go off...
                            vm.constructionList.sort((a, b) => (a.description > b.description)
                                ? 1
                                : ((b.templateName > a.templateName)
                                    ? 1
                                    : -1));
                        });

                });
        }

        function setActionButtons() {
            vm.actionButtons = [];
            vm.actionButtons.push({
                onclick: vm.createConstruction,
                name: 'Add New',
                desc: 'Add New',
                roles: ['settings__settings__create'],
            });
        }

        vm.addCustomFilter = function () {

            let categoriesTransformed = vm.constructionCategories.map(x => ({ title: x.title, value: x.constructionCategoryCode }));
            // Just match on description otherwise the search filter will display the id...
            let manufacturersTransformed = vm.manufacturers.map(x => ({ title: x.description, value: x.description }));

            // field -> the field to filter by
            let options = []

            options.push({
                title: vm.isConstruction() ? "Construction Category" : "Opening Category",
                field: 'constructionCategoryCode',
                operator: 'eq',
                elementType: "select",
                values: categoriesTransformed
            });

            // SubCategory / OpeningStyle - Conditional
            // if (vm.isConstruction()) {
            // const subCategoriesTransformed = vm.constructionSubCategories.map(x => ({ title: x.title, value: x.constructionCategoryCode }));
            // options.push({
            //         title: "Construction Subcategory",
            //         field: 'constructionSubCategoryCode',
            //         operator: 'eq',
            //         elementType: "select",
            //         values: subCategoriesTransformed
            //     });
            // }
            // else
            if (!vm.isConstruction()) {
                const openingStylesTransformed = vm.openingStyles.map(x => ({ title: x.title, value: x.openingStyleCode }));
                options.push({
                    title: "Opening Style",
                    field: 'openingStyleCode',
                    operator: 'eq',
                    elementType: "select",
                    values: openingStylesTransformed
                });
            }

            options.push({
                title: "Manufacturer",
                field: 'manufacturerDescription',
                operator: 'eq',
                elementType: "select",
                values: manufacturersTransformed
            });

            var modalScope = $rootScope.$new(true);
            modalScope.options = options;

            $mdDialog.show({
                templateUrl: 'app/ui/data/generic-filter-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: true,
                scope: modalScope,
                skipHide: true, // DON'T HIDE THE MODAL
            })
                .then(function (response) {
                    vm.customFilter = response;
                    vm.refreshList(vm.customFilter);
                    localStorage.setItem(FILTER_NAME, JSON.stringify(vm.customFilter));

                }, function () { });
        };

        vm.removeCustomFilter = function (f) {

            vm.customFilter = vm.customFilter.filter(x => x.field != f.field);

            if (vm.customFilter.length == 0) {
                vm.customFilter = null;
            }

            localStorage.setItem(FILTER_NAME, JSON.stringify(vm.customFilter));
            vm.refreshList(vm.customFilter);
        };

        vm.getPreviousCustomFilters = function () {
            var savedState = JSON.parse(localStorage.getItem(FILTER_NAME));

            if (savedState) {
                vm.customFilter = savedState;
                vm.refreshList(vm.customFilter);
            }
        }

        setTimeout(() => vm.getPreviousCustomFilters(), 200);

        vm.createCustomFilterLabel = function(field) {
            let overriddenField = field;

            // Override for UI
            if (field === 'manufacturerDescription')
                overriddenField = 'Manufacturer';
            else if (field === 'constructionCategoryCode')
                overriddenField = 'Construction Category';
            else
                overriddenField = vm.toSplitTitleCase(overriddenField);

            return overriddenField;
        }

        vm.toSplitTitleCase = function (string) {
            return common.toSplitTitleCase(string);
        }

        vm.bulkSelect = function (state) {
            console.log(vm.constructionList);
            vm.constructionList
                .filter(x => x.constructionCategoryCode !== "VerticalOpening" &&
                             x.constructionCategoryCode !== "HorizontalOpening")
                .forEach(x => x.isBulkSelected = state);
        }

        vm.bulkSelectionsExist = function () {
            return vm.constructionList.some(x => x.isBulkSelected);
        }

        vm.bulkDelete = async function() {
            let toDelete = vm.constructionList.filter(x => x.isBulkSelected);
            const deletePromises = [];

            for (let i = 0; i < toDelete.length; i++) {
                deletePromises.push(constructionservice.deleteConstruction(toDelete[i].constructionId, toDelete[i].type, false));
            }

            await Promise.all(deletePromises);

            toDelete.forEach(item => {
                item.deleted = true;
            });

            vm.constructionList = vm.constructionList.filter(x => !x.deleted);

            vm.constructionList.forEach(item => item.isBulkSelected = false);
            vm.bulkSelected = false;

            common.logger.logSuccess(`${toDelete.length} Constructions deleted successfully`);
        }

        vm.isPermanentOpening = function(row) {
            return row.constructionCategoryCode === "VerticalOpening" || row.constructionCategoryCode === "HorizontalOpening";
        }

        setActionButtons();

        // -------------- //
        // - INITIALISE - //
        // -------------- //

        vm.initialise = function () {
            vm.clearFilters();
        }

        /**
         * Attempts to process the given excel files. Will show a dialog with
         * further actions required depending on how the processing went
         * (i.e. if any warnings or errors were encountered)
         *
         * @param {any} file Excel file to upload
         */
        vm.uploadFile = function (file) {

            if (file == null)
                return;

            constructionservice.uploadConstructionDatabase($stateParams.type, file, false)
                .then((data) => {
                    var modalScope = $rootScope.$new();
                    modalScope.excelData = file;
                    modalScope.response = data;
                    modalScope.type = $stateParams.type;
                    modalScope.isInitialProcess = true;
                    modalScope.extractor = vm.templateType.toLowerCase();

                    modalScope.modalInstance = $mdDialog.show({
                        templateUrl: 'app/ui/data/construction/construction-import.html',
                        scope: modalScope,
                    });
                });
        }

        /**
         * Exports the database to an Excel file in the same format as expected for imports
         */
        vm.exportDatabase = function () {
            vm.isBusy = true;
            constructionservice.exportConstructionDatabase($stateParams.type)
                .then(() => {
                    vm.isBusy = false;
                })
                .catch(() => {
                    vm.isBusy = false;
                });
        }

        vm.isConstruction = function() {
            return vm.templateType.toLowerCase() === 'construction' || vm.templateType.toLowerCase() === 'surface';
        }

        vm.goToConstruction = function(constructionId, type) {
            $state.go("construction-updateform", { constructionId: constructionId, type: type });
        }

        vm.setFavouriteStatus = function(constructionId, isFavourite) {
            let type = 'surface';
            const item = vm.constructionList.find(x => x.constructionId === constructionId);
            if (item && item.type) {
                type = item.type.toLowerCase();
            }

            constructionservice.setIsFavourite(constructionId, isFavourite, type)
                .then(function() {
                    if (item) {
                        item.isFavourite = isFavourite;
                    }
                });
        }

        vm.showBulkEditModal = function() {
            let selectedItems = vm.constructionList.filter(x => x.isBulkSelected);

            if (selectedItems.length === 0) {
                return;
            }

            var modalScope = $rootScope.$new();
            modalScope.title = "Bulk Edit";

            $mdDialog.show({
                templateUrl: 'app/ui/data/construction/bulk-edit-construction-modal.html',
                scope: modalScope,
                clickOutsideToClose: false
            }).then(function(action) {
                if (action === 'DELETE') {
                    vm.bulkDelete();
                } else if (action === 'COPY') {
                    vm.bulkCopy(selectedItems);
                } else if (action === 'EXPORT') {
                    vm.bulkExport();
                }
            });
        }

        vm.bulkCopy = function(items) {
            const promises = [];

            items.forEach(item => {
                promises.push(constructionservice
                    .copyConstruction(item.constructionId, item.type, false)
                    .then((id) => {
                        return constructionservice
                            .getConstruction(id, item.type)
                            .then(template => {
                                let transform = {
                                    constructionId: template.constructionId,
                                    description: template.description,
                                    constructionCategoryTitle: template.category.title,
                                    type: template.category.type == 'surface' ? 'surface' : 'opening'
                                };

                                vm.constructionList.push(transform);
                            });
                    }));
            });

            Promise.all(promises).then(() => {
                vm.constructionList.sort((a, b) => (a.description > b.description)
                    ? 1
                    : ((b.templateName > a.templateName)
                        ? 1
                        : -1));

                items.forEach(item => item.isBulkSelected = false);
                vm.bulkSelected = false;

                common.logger.logSuccess(`${items.length} Constructions copied successfully`);
            });
        }

        vm.bulkExport = function () {
            vm.isBusy = true;
            let selectedItems = vm.constructionList.filter(x => x.isBulkSelected);
            let selectedIds = selectedItems.map(item => item.constructionId);

            constructionservice.exportConstructionDatabase(vm.templateType, selectedIds)
                .then(() => {
                    vm.isBusy = false;

                    // Clear all selections
                    selectedItems.forEach(item => item.isBulkSelected = false);
                    vm.bulkSelected = false;

                    // Show a success message
                    common.logger.logSuccess(`${selectedItems.length} ${vm.templateType} items exported successfully`);
                })
                .catch(() => {
                    vm.isBusy = false;
                });
        }

        // Initialize the page
        vm.initialise();
    }
})();